"use client";

import { useState } from "react";

import * as Dialog from "@radix-ui/react-dialog";
import * as Select from "@radix-ui/react-select";
import * as Switch from "@radix-ui/react-switch";
import * as Tooltip from "@radix-ui/react-tooltip";
import { useForm, Controller } from "react-hook-form";

import { formatDate } from "@/lib/utils";
import type { Fund } from "@/types/fund";

interface EnhancedFundSelectorProperties {
  funds: Fund[];
  selectedFund: Fund | null;
  onFundSelect: (fund: Fund) => void;
  className?: string;
}

interface FilterForm {
  fundType: string;
  riskLevel: string;
  minReturn: string;
  maxReturn: string;
  useAdvancedFilter: boolean;
}

export default function EnhancedFundSelector({
  funds,
  selectedFund,
  onFundSelect,
  className = "",
}: EnhancedFundSelectorProperties) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const { control, watch, reset } = useForm<FilterForm>({
    defaultValues: {
      fundType: "",
      riskLevel: "",
      minReturn: "",
      maxReturn: "",
      useAdvancedFilter: false,
    },
  });

  const formValues = watch();

  // 过滤基金
  const filteredFunds = funds.filter((fund) => {
    // 基础搜索
    const matchesSearch =
      fund.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fund.code.includes(searchTerm);

    if (!formValues.useAdvancedFilter) {
      return matchesSearch;
    }

    // 高级过滤
    const matchesType =
      !formValues.fundType || fund.type === formValues.fundType;
    const matchesRisk =
      !formValues.riskLevel || fund.riskLevel === formValues.riskLevel;

    let matchesReturn = true;
    if (formValues.minReturn && fund.yearReturn) {
      matchesReturn =
        fund.yearReturn >= Number.parseFloat(formValues.minReturn);
    }
    if (formValues.maxReturn && fund.yearReturn) {
      matchesReturn =
        matchesReturn &&
        fund.yearReturn <= Number.parseFloat(formValues.maxReturn);
    }

    return matchesSearch && matchesType && matchesRisk && matchesReturn;
  });

  const getFundTypeColor = (type: Fund["type"]) => {
    const typeColors = {
      stock: "bg-red-100 text-red-800",
      bond: "bg-green-100 text-green-800",
      hybrid: "bg-blue-100 text-blue-800",
      index: "bg-purple-100 text-purple-800",
      money: "bg-yellow-100 text-yellow-800",
    };
    return typeColors[type];
  };

  const getRiskLevelColor = (level: string) => {
    const colors = {
      low: "text-green-600",
      medium: "text-yellow-600",
      high: "text-red-600",
    };
    return colors[level as keyof typeof colors] || "text-gray-600";
  };

  return (
    <Tooltip.Provider>
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">选择基金</h3>

          <Dialog.Root open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <Dialog.Trigger asChild>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                高级筛选
              </button>
            </Dialog.Trigger>

            <Dialog.Portal>
              <Dialog.Overlay className="fixed inset-0 bg-black/50 z-40" />
              <Dialog.Content className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-lg p-6 w-full max-w-md z-50 shadow-xl">
                <Dialog.Title className="text-lg font-semibold mb-4">
                  高级筛选设置
                </Dialog.Title>

                <div className="space-y-4">
                  {/* 启用高级筛选开关 */}
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">启用高级筛选</label>
                    <Controller
                      name="useAdvancedFilter"
                      control={control}
                      render={({ field }) => (
                        <Switch.Root
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="w-11 h-6 bg-gray-200 rounded-full relative data-[state=checked]:bg-blue-600 transition-colors"
                        >
                          <Switch.Thumb className="block w-5 h-5 bg-white rounded-full transition-transform duration-100 translate-x-0.5 will-change-transform data-[state=checked]:translate-x-[22px]" />
                        </Switch.Root>
                      )}
                    />
                  </div>

                  {formValues.useAdvancedFilter ? (
                    <>
                      {/* 基金类型 */}
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          基金类型
                        </label>
                        <Controller
                          name="fundType"
                          control={control}
                          render={({ field }) => (
                            <Select.Root
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <Select.Value placeholder="选择基金类型" />
                                <Select.Icon className="ml-auto">
                                  <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 9l-7 7-7-7"
                                    />
                                  </svg>
                                </Select.Icon>
                              </Select.Trigger>

                              <Select.Portal>
                                <Select.Content className="bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                                  <Select.Viewport className="p-1">
                                    <Select.Item
                                      value=""
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>
                                        全部类型
                                      </Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="stock"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>股票型</Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="bond"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>债券型</Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="hybrid"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>混合型</Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="index"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>指数型</Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="money"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>货币型</Select.ItemText>
                                    </Select.Item>
                                  </Select.Viewport>
                                </Select.Content>
                              </Select.Portal>
                            </Select.Root>
                          )}
                        />
                      </div>

                      {/* 风险等级 */}
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          风险等级
                        </label>
                        <Controller
                          name="riskLevel"
                          control={control}
                          render={({ field }) => (
                            <Select.Root
                              value={field.value}
                              onValueChange={field.onChange}
                            >
                              <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <Select.Value placeholder="选择风险等级" />
                                <Select.Icon className="ml-auto">
                                  <svg
                                    className="w-4 h-4"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M19 9l-7 7-7-7"
                                    />
                                  </svg>
                                </Select.Icon>
                              </Select.Trigger>

                              <Select.Portal>
                                <Select.Content className="bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                                  <Select.Viewport className="p-1">
                                    <Select.Item
                                      value=""
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>
                                        全部等级
                                      </Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="low"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>低风险</Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="medium"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>中风险</Select.ItemText>
                                    </Select.Item>
                                    <Select.Item
                                      value="high"
                                      className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                                    >
                                      <Select.ItemText>高风险</Select.ItemText>
                                    </Select.Item>
                                  </Select.Viewport>
                                </Select.Content>
                              </Select.Portal>
                            </Select.Root>
                          )}
                        />
                      </div>

                      {/* 年化收益率范围 */}
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <label className="block text-sm font-medium mb-2">
                            最低收益率(%)
                          </label>
                          <Controller
                            name="minReturn"
                            control={control}
                            render={({ field }) => (
                              <input
                                {...field}
                                type="number"
                                step="0.1"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="0"
                              />
                            )}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-2">
                            最高收益率(%)
                          </label>
                          <Controller
                            name="maxReturn"
                            control={control}
                            render={({ field }) => (
                              <input
                                {...field}
                                type="number"
                                step="0.1"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                placeholder="100"
                              />
                            )}
                          />
                        </div>
                      </div>
                    </>
                  ) : null}
                </div>

                <div className="flex justify-end space-x-3 mt-6">
                  <button
                    onClick={() => {
                      reset();
                    }}
                    className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    重置
                  </button>
                  <Dialog.Close asChild>
                    <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                      确定
                    </button>
                  </Dialog.Close>
                </div>
              </Dialog.Content>
            </Dialog.Portal>
          </Dialog.Root>
        </div>

        {/* 搜索框 */}
        <div className="relative">
          <input
            type="text"
            placeholder="搜索基金名称或代码..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
            }}
            className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <svg
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>

        {/* 筛选状态 */}
        {formValues.useAdvancedFilter ? (
          <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
            <span className="font-medium">高级筛选已启用</span>
            <span className="ml-2">找到 {filteredFunds.length} 只基金</span>
          </div>
        ) : null}

        {/* 基金列表 */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredFunds.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || formValues.useAdvancedFilter
                ? "未找到匹配的基金"
                : "暂无基金数据"}
            </div>
          ) : (
            filteredFunds.map((fund) => (
              <Tooltip.Root key={fund.id}>
                <Tooltip.Trigger asChild>
                  <div
                    onClick={() => {
                      onFundSelect(fund);
                    }}
                    className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                      selectedFund?.id === fund.id
                        ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200"
                        : "border-gray-200 hover:border-gray-300"
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="font-medium text-gray-900 truncate">
                            {fund.name}
                          </h4>
                          <span
                            className={`px-2 py-1 text-xs rounded-full ${getFundTypeColor(fund.type)}`}
                          >
                            {fund.type === "stock"
                              ? "股票"
                              : fund.type === "bond"
                                ? "债券"
                                : fund.type === "hybrid"
                                  ? "混合"
                                  : fund.type === "index"
                                    ? "指数"
                                    : "货币"}
                          </span>
                        </div>

                        <p className="text-sm text-gray-600 mb-1">
                          代码: {fund.code}
                        </p>

                        <div className="flex items-center space-x-4 text-sm">
                          {fund.yearReturn ? (
                            <span
                              className={`font-medium ${fund.yearReturn >= 0 ? "text-green-600" : "text-red-600"}`}
                            >
                              年化收益: {fund.yearReturn.toFixed(2)}%
                            </span>
                          ) : null}
                          {fund.riskLevel ? (
                            <span className={getRiskLevelColor(fund.riskLevel)}>
                              风险:{" "}
                              {fund.riskLevel === "low"
                                ? "低"
                                : fund.riskLevel === "medium"
                                  ? "中"
                                  : "高"}
                            </span>
                          ) : null}
                        </div>
                      </div>
                    </div>
                  </div>
                </Tooltip.Trigger>

                <Tooltip.Portal>
                  <Tooltip.Content
                    className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm max-w-xs z-50"
                    sideOffset={5}
                  >
                    <div className="space-y-1">
                      <div className="font-medium">{fund.name}</div>
                      <div>代码: {fund.code}</div>
                      {fund.manager ? (
                        <div>基金经理: {fund.manager}</div>
                      ) : null}
                      {fund.company ? (
                        <div>基金公司: {fund.company}</div>
                      ) : null}
                      {fund.establishDate ? (
                        <div>
                          成立日期:{" "}
                          {formatDate(fund.establishDate, "YYYY年MM月DD日")}
                        </div>
                      ) : null}
                    </div>
                    <Tooltip.Arrow className="fill-gray-900" />
                  </Tooltip.Content>
                </Tooltip.Portal>
              </Tooltip.Root>
            ))
          )}
        </div>
      </div>
    </Tooltip.Provider>
  );
}
