"use client";

import { useState } from "react";

import * as Select from "@radix-ui/react-select";
import * as Tooltip from "@radix-ui/react-tooltip";
import {
  format,
  parseISO,
  isValid,
  subDays,
  subMonths,
  subYears,
  startOfDay,
  endOfDay,
  differenceInDays,
  isAfter,
  isBefore,
} from "date-fns";
import { zhCN } from "date-fns/locale";
import { useForm, Controller } from "react-hook-form";

interface DateRangePickerProperties {
  startDate: string;
  endDate: string;
  onDateRangeChange: (startDate: string, endDate: string) => void;
  className?: string;
}

interface DateRangeForm {
  startDate: string;
  endDate: string;
  quickSelect: string;
}

const quickSelectOptions = [
  { value: "", label: "自定义范围" },
  { value: "7d", label: "最近7天" },
  { value: "1m", label: "最近1个月" },
  { value: "3m", label: "最近3个月" },
  { value: "6m", label: "最近6个月" },
  { value: "1y", label: "最近1年" },
  { value: "2y", label: "最近2年" },
  { value: "3y", label: "最近3年" },
  { value: "5y", label: "最近5年" },
];

export default function DateRangePicker({
  startDate,
  endDate,
  onDateRangeChange,
  className = "",
}: DateRangePickerProperties) {
  const [errors, setErrors] = useState<{
    startDate?: string;
    endDate?: string;
  }>({});

  const { control, watch, setValue, handleSubmit } = useForm<DateRangeForm>({
    defaultValues: {
      startDate,
      endDate,
      quickSelect: "",
    },
  });

  const formValues = watch();

  // 验证日期范围
  const validateDateRange = (start: string, end: string) => {
    const newErrors: { startDate?: string; endDate?: string } = {};

    if (!start) {
      newErrors.startDate = "请选择开始日期";
    } else if (!isValid(parseISO(start))) {
      newErrors.startDate = "开始日期格式不正确";
    }

    if (!end) {
      newErrors.endDate = "请选择结束日期";
    } else if (!isValid(parseISO(end))) {
      newErrors.endDate = "结束日期格式不正确";
    }

    if (start && end && isValid(parseISO(start)) && isValid(parseISO(end))) {
      const startDateObject = parseISO(start);
      const endDateObject = parseISO(end);

      if (isAfter(startDateObject, endDateObject)) {
        newErrors.startDate = "开始日期不能晚于结束日期";
      }

      const daysDiff = differenceInDays(endDateObject, startDateObject);
      if (daysDiff > 365 * 10) {
        newErrors.endDate = "日期范围不能超过10年";
      }

      // 检查是否是未来日期
      const today = new Date();
      if (isAfter(startDateObject, today)) {
        newErrors.startDate = "开始日期不能是未来日期";
      }
      if (isAfter(endDateObject, today)) {
        newErrors.endDate = "结束日期不能是未来日期";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理快速选择
  const handleQuickSelect = (value: string) => {
    if (!value) return;

    const today = endOfDay(new Date());
    let newStartDate: Date;

    switch (value) {
      case "7d": {
        newStartDate = startOfDay(subDays(today, 7));
        break;
      }
      case "1m": {
        newStartDate = startOfDay(subMonths(today, 1));
        break;
      }
      case "3m": {
        newStartDate = startOfDay(subMonths(today, 3));
        break;
      }
      case "6m": {
        newStartDate = startOfDay(subMonths(today, 6));
        break;
      }
      case "1y": {
        newStartDate = startOfDay(subYears(today, 1));
        break;
      }
      case "2y": {
        newStartDate = startOfDay(subYears(today, 2));
        break;
      }
      case "3y": {
        newStartDate = startOfDay(subYears(today, 3));
        break;
      }
      case "5y": {
        newStartDate = startOfDay(subYears(today, 5));
        break;
      }
      default: {
        return;
      }
    }

    const newStartDateString = format(newStartDate, "yyyy-MM-dd");
    const newEndDateString = format(today, "yyyy-MM-dd");

    setValue("startDate", newStartDateString);
    setValue("endDate", newEndDateString);
    setValue("quickSelect", value);

    if (validateDateRange(newStartDateString, newEndDateString)) {
      onDateRangeChange(newStartDateString, newEndDateString);
    }
  };

  // 处理日期变化
  const handleDateChange = (field: "startDate" | "endDate", value: string) => {
    setValue(field, value);
    setValue("quickSelect", ""); // 清除快速选择

    const otherField = field === "startDate" ? "endDate" : "startDate";
    const otherValue = formValues[otherField];

    if (
      validateDateRange(
        field === "startDate" ? value : otherValue,
        field === "endDate" ? value : otherValue
      )
    ) {
      onDateRangeChange(
        field === "startDate" ? value : otherValue,
        field === "endDate" ? value : otherValue
      );
    }
  };

  // 格式化日期显示
  const formatDateDisplay = (dateString: string) => {
    try {
      const date = parseISO(dateString);
      if (!isValid(date)) return dateString;
      return format(date, "yyyy年MM月dd日", { locale: zhCN });
    } catch {
      return dateString;
    }
  };

  // 计算日期范围信息
  const getDateRangeInfo = () => {
    if (!formValues.startDate || !formValues.endDate) return null;

    try {
      const start = parseISO(formValues.startDate);
      const end = parseISO(formValues.endDate);

      if (!isValid(start) || !isValid(end)) return null;

      const days = differenceInDays(end, start);
      const years = Math.floor(days / 365);
      const months = Math.floor((days % 365) / 30);
      const remainingDays = days % 30;

      let duration = "";
      if (years > 0) duration += `${years}年`;
      if (months > 0) duration += `${months}个月`;
      if (remainingDays > 0 || duration === "")
        duration += `${remainingDays}天`;

      return {
        days,
        duration,
        startFormatted: formatDateDisplay(formValues.startDate),
        endFormatted: formatDateDisplay(formValues.endDate),
      };
    } catch {
      return null;
    }
  };

  const dateRangeInfo = getDateRangeInfo();

  return (
    <Tooltip.Provider>
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">选择日期范围</h3>
          {dateRangeInfo ? (
            <Tooltip.Root>
              <Tooltip.Trigger asChild>
                <div className="text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                  {dateRangeInfo.duration}
                </div>
              </Tooltip.Trigger>
              <Tooltip.Portal>
                <Tooltip.Content
                  className="bg-gray-900 text-white px-3 py-2 rounded-lg text-sm z-50"
                  sideOffset={5}
                >
                  <div className="space-y-1">
                    <div>开始: {dateRangeInfo.startFormatted}</div>
                    <div>结束: {dateRangeInfo.endFormatted}</div>
                    <div>总计: {dateRangeInfo.days} 天</div>
                  </div>
                  <Tooltip.Arrow className="fill-gray-900" />
                </Tooltip.Content>
              </Tooltip.Portal>
            </Tooltip.Root>
          ) : null}
        </div>

        {/* 快速选择 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            快速选择
          </label>
          <Controller
            name="quickSelect"
            control={control}
            render={({ field }) => (
              <Select.Root
                value={field.value}
                onValueChange={handleQuickSelect}
              >
                <Select.Trigger className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                  <Select.Value placeholder="选择时间范围" />
                  <Select.Icon className="ml-auto">
                    <svg
                      className="w-4 h-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M19 9l-7 7-7-7"
                      />
                    </svg>
                  </Select.Icon>
                </Select.Trigger>

                <Select.Portal>
                  <Select.Content className="bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                    <Select.Viewport className="p-1">
                      {quickSelectOptions.map((option) => (
                        <Select.Item
                          key={option.value}
                          value={option.value}
                          className="px-3 py-2 hover:bg-gray-100 rounded cursor-pointer"
                        >
                          <Select.ItemText>{option.label}</Select.ItemText>
                        </Select.Item>
                      ))}
                    </Select.Viewport>
                  </Select.Content>
                </Select.Portal>
              </Select.Root>
            )}
          />
        </div>

        {/* 自定义日期选择 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              开始日期
            </label>
            <Controller
              name="startDate"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="date"
                  onChange={(e) => {
                    handleDateChange("startDate", e.target.value);
                  }}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.startDate
                      ? "border-red-300 bg-red-50"
                      : "border-gray-300"
                  }`}
                />
              )}
            />
            {errors.startDate ? (
              <p className="text-sm text-red-600 mt-1 flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                {errors.startDate}
              </p>
            ) : null}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              结束日期
            </label>
            <Controller
              name="endDate"
              control={control}
              render={({ field }) => (
                <input
                  {...field}
                  type="date"
                  onChange={(e) => {
                    handleDateChange("endDate", e.target.value);
                  }}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.endDate
                      ? "border-red-300 bg-red-50"
                      : "border-gray-300"
                  }`}
                />
              )}
            />
            {errors.endDate ? (
              <p className="text-sm text-red-600 mt-1 flex items-center">
                <svg
                  className="w-4 h-4 mr-1"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                {errors.endDate}
              </p>
            ) : null}
          </div>
        </div>

        {/* 日期范围预览 */}
        {dateRangeInfo && Object.keys(errors).length === 0 ? (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center">
              <svg
                className="w-5 h-5 text-green-400 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <div className="text-sm text-green-800">
                <div className="font-medium">
                  {dateRangeInfo.startFormatted} 至 {dateRangeInfo.endFormatted}
                </div>
                <div>
                  回测期间: {dateRangeInfo.duration} ({dateRangeInfo.days} 天)
                </div>
              </div>
            </div>
          </div>
        ) : null}
      </div>
    </Tooltip.Provider>
  );
}
