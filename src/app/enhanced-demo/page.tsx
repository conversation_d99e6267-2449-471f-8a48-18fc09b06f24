"use client";

import { useState } from "react";

import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

import DateRangePicker from "@/components/DateRangePicker";
import EnhancedFundSelector from "@/components/EnhancedFundSelector";
import EnhancedParameterForm from "@/components/EnhancedParameterForm";
import type { Fund, Strategy } from "@/types/fund";

// 模拟基金数据
const mockFunds: Fund[] = [
  {
    id: "fund_001",
    code: "000001",
    name: "华夏成长混合",
    type: "hybrid",
    riskLevel: "medium",
    yearReturn: 12.5,
    manager: "张三",
    company: "华夏基金",
    establishDate: "2020-01-01",
  },
  {
    id: "fund_002",
    code: "000002",
    name: "易方达蓝筹精选混合",
    type: "hybrid",
    riskLevel: "medium",
    yearReturn: 15.8,
    manager: "李四",
    company: "易方达基金",
    establishDate: "2019-06-15",
  },
  {
    id: "fund_003",
    code: "000003",
    name: "南方中证500ETF",
    type: "index",
    riskLevel: "high",
    yearReturn: 8.2,
    manager: "王五",
    company: "南方基金",
    establishDate: "2018-03-20",
  },
  {
    id: "fund_004",
    code: "000004",
    name: "招商中证白酒指数",
    type: "index",
    riskLevel: "high",
    yearReturn: 22.1,
    manager: "赵六",
    company: "招商基金",
    establishDate: "2017-12-01",
  },
  {
    id: "fund_005",
    code: "000005",
    name: "天弘余额宝货币",
    type: "money",
    riskLevel: "low",
    yearReturn: 2.1,
    manager: "钱七",
    company: "天弘基金",
    establishDate: "2013-05-29",
  },
];

// 模拟策略数据
const mockStrategy: Strategy = {
  id: "fixed_amount",
  name: "定额投资策略",
  description: "每月固定金额投资，适合长期投资",
  parameterSchema: {
    monthlyAmount: {
      type: "number",
      label: "月投资金额",
      description: "每月投资的固定金额",
      required: true,
      min: 100,
      max: 50_000,
      step: 100,
      defaultValue: 1000,
    },
    startDate: {
      type: "date",
      label: "开始日期",
      description: "投资开始日期",
      required: true,
      defaultValue: "2020-01-01",
    },
    endDate: {
      type: "date",
      label: "结束日期",
      description: "投资结束日期",
      required: true,
      defaultValue: "2024-01-01",
    },
    investmentDay: {
      type: "select",
      label: "投资日",
      description: "每月的投资日期",
      required: true,
      defaultValue: "1",
      options: [
        { value: "1", label: "每月1号" },
        { value: "15", label: "每月15号" },
        { value: "last", label: "每月最后一天" },
      ],
    },
    riskTolerance: {
      type: "range",
      label: "风险承受度",
      description: "投资风险承受度 (1-10)",
      required: true,
      min: 1,
      max: 10,
      step: 1,
      defaultValue: 5,
    },
  },
};

export default function EnhancedDemoPage() {
  const [selectedFund, setSelectedFund] = useState<Fund | null>(null);
  const [parameters, setParameters] = useState<Record<string, any>>({});
  const [startDate, setStartDate] = useState("2020-01-01");
  const [endDate, setEndDate] = useState("2024-01-01");

  const handleFundSelect = (fund: Fund) => {
    setSelectedFund(fund);
  };

  const handleParametersChange = (newParameters: Record<string, any>) => {
    setParameters(newParameters);
  };

  const handleDateRangeChange = (newStartDate: string, newEndDate: string) => {
    setStartDate(newStartDate);
    setEndDate(newEndDate);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            增强组件演示
          </h1>
          <p className="text-lg text-gray-600">
            展示使用 date-fns、react-hook-form 和 Radix UI 的增强组件
          </p>
        </div>

        {/* 功能特性说明 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <svg
                  className="w-5 h-5 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900">date-fns</h3>
            </div>
            <p className="text-sm text-gray-600">
              强大的日期处理库，支持国际化、时区处理和丰富的日期操作功能
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <svg
                  className="w-5 h-5 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900">React Hook Form</h3>
            </div>
            <p className="text-sm text-gray-600">
              高性能表单库，提供强大的验证、错误处理和表单状态管理功能
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center mb-3">
              <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <svg
                  className="w-5 h-5 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                  />
                </svg>
              </div>
              <h3 className="font-semibold text-gray-900">Radix UI</h3>
            </div>
            <p className="text-sm text-gray-600">
              无样式、可访问的UI组件库，提供专业级的交互体验和键盘导航
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：基金选择和日期范围 */}
          <div className="space-y-8">
            {/* 增强基金选择器 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <EnhancedFundSelector
                funds={mockFunds}
                selectedFund={selectedFund}
                onFundSelect={handleFundSelect}
              />
            </div>

            {/* 日期范围选择器 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <DateRangePicker
                startDate={startDate}
                endDate={endDate}
                onDateRangeChange={handleDateRangeChange}
              />
            </div>
          </div>

          {/* 右侧：参数设置和预览 */}
          <div className="space-y-8">
            {/* 增强参数表单 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <EnhancedParameterForm
                strategy={mockStrategy}
                parameters={parameters}
                onParametersChange={handleParametersChange}
              />
            </div>

            {/* 配置预览 */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                配置预览
              </h3>

              <div className="space-y-4">
                {/* 选中的基金 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">选中基金</h4>
                  {selectedFund ? (
                    <div className="space-y-1 text-sm">
                      <div>
                        <span className="text-gray-600">名称:</span>{" "}
                        {selectedFund.name}
                      </div>
                      <div>
                        <span className="text-gray-600">代码:</span>{" "}
                        {selectedFund.code}
                      </div>
                      <div>
                        <span className="text-gray-600">类型:</span>{" "}
                        {selectedFund.type}
                      </div>
                      <div>
                        <span className="text-gray-600">年化收益:</span>{" "}
                        {selectedFund.yearReturn}%
                      </div>
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">请选择基金</p>
                  )}
                </div>

                {/* 日期范围 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">回测期间</h4>
                  <div className="space-y-1 text-sm">
                    <div>
                      <span className="text-gray-600">开始:</span>{" "}
                      {format(new Date(startDate), "yyyy年MM月dd日", {
                        locale: zhCN,
                      })}
                    </div>
                    <div>
                      <span className="text-gray-600">结束:</span>{" "}
                      {format(new Date(endDate), "yyyy年MM月dd日", {
                        locale: zhCN,
                      })}
                    </div>
                  </div>
                </div>

                {/* 策略参数 */}
                <div className="p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2">策略参数</h4>
                  {Object.keys(parameters).length > 0 ? (
                    <div className="space-y-1 text-sm">
                      {Object.entries(parameters).map(([key, value]) => (
                        <div key={key}>
                          <span className="text-gray-600">{key}:</span>{" "}
                          {String(value)}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">请设置策略参数</p>
                  )}
                </div>
              </div>

              {/* 开始回测按钮 */}
              <button
                disabled={!selectedFund || Object.keys(parameters).length === 0}
                className={`w-full mt-6 px-4 py-3 rounded-lg font-medium transition-colors ${
                  selectedFund && Object.keys(parameters).length > 0
                    ? "bg-blue-600 text-white hover:bg-blue-700"
                    : "bg-gray-300 text-gray-500 cursor-not-allowed"
                }`}
              >
                开始回测
              </button>
            </div>
          </div>
        </div>

        {/* 底部说明 */}
        <div className="mt-12 bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            组件特性说明
          </h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-2">增强功能</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 使用 date-fns 进行专业的日期处理</li>
                <li>• React Hook Form 提供强大的表单验证</li>
                <li>• Radix UI 组件提供无障碍访问支持</li>
                <li>• 实时表单验证和错误提示</li>
                <li>• 智能日期范围选择和验证</li>
                <li>• 高级基金筛选功能</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-2">用户体验</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• 响应式设计，支持移动端</li>
                <li>• 键盘导航支持</li>
                <li>• 工具提示和帮助信息</li>
                <li>• 实时参数预览</li>
                <li>• 优雅的加载和错误状态</li>
                <li>• 专业的视觉反馈</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
