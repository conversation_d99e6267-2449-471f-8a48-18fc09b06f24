import type { Fund } from "@/types/fund";

/**
 * Mock data functions for development and testing
 * These functions provide placeholder implementations
 */

// Sample fund data for development
const sampleFunds: Fund[] = [
  {
    id: "fund_001",
    name: "华夏成长混合",
    code: "000001",
    type: "hybrid",
    riskLevel: "medium",
    indexId: "hs300",
  },
  {
    id: "fund_002",
    name: "大成高新技术产业股票",
    code: "000628",
    type: "equity",
    riskLevel: "high",
    indexId: "zz500",
  },
  {
    id: "fund_003",
    name: "易方达蓝筹精选混合",
    code: "005827",
    type: "hybrid",
    riskLevel: "medium",
    indexId: "hs300",
  },
];

/**
 * Get all available funds
 */
export async function getFunds(): Promise<Fund[]> {
  // Simulate async operation
  await new Promise((resolve) => setTimeout(resolve, 100));
  return sampleFunds;
}

/**
 * Search funds by name or code
 */
export async function searchFunds(query: string): Promise<Fund[]> {
  // Simulate async operation
  await new Promise((resolve) => setTimeout(resolve, 100));

  const normalizedQuery = query.toLowerCase().trim();

  return sampleFunds.filter(
    (fund) =>
      fund.name.toLowerCase().includes(normalizedQuery) ||
      fund.code.includes(normalizedQuery)
  );
}

/**
 * Validate if a fund code exists
 */
export async function validateFund(fundCode: string): Promise<boolean> {
  // Simulate async operation
  await new Promise((resolve) => setTimeout(resolve, 100));

  // Basic validation - check if it's a 6-digit code
  if (!/^\d{6}$/.test(fundCode)) {
    return false;
  }

  // Check if it exists in our sample data or use basic validation
  const exists = sampleFunds.some((fund) => fund.code === fundCode);

  // For codes not in sample data, assume they're valid if format is correct
  return exists || true;
}
