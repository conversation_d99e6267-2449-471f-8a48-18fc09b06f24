import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import {
  APP_CONFIG,
  ENV_CONFIG,
  getConfig,
  validateConfig,
} from "@/lib/config";

describe("Config", () => {
  const originalEnv = process.env;

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variables
    process.env = { ...originalEnv };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe("APP_CONFIG", () => {
    it("应该包含基本应用信息", () => {
      expect(APP_CONFIG.name).toBe("基金投资策略回测计算器");
      expect(APP_CONFIG.version).toBe("1.0.0");
      expect(APP_CONFIG.description).toContain("基金投资策略回测分析工具");
    });

    it("应该包含默认设置", () => {
      expect(APP_CONFIG.defaults).toBeDefined();
      expect(APP_CONFIG.defaults.startDate).toBe("2020-01-01");
      expect(APP_CONFIG.defaults.endDate).toBe("2024-01-01");
      expect(APP_CONFIG.defaults.initialAmount).toBe(10_000);
      expect(APP_CONFIG.defaults.monthlyAmount).toBe(1000);
      expect(APP_CONFIG.defaults.strategy).toBe("fixed_amount");
      expect(APP_CONFIG.defaults.fund).toBe("fund_001");
    });

    it("应该包含数据源配置", () => {
      expect(APP_CONFIG.dataSource).toBeDefined();
      expect(APP_CONFIG.dataSource.api.baidu.baseUrl).toBe(
        "https://gushitong.baidu.com/opendata"
      );
      expect(APP_CONFIG.dataSource.api.baidu.resourceId).toBe("5824");
      expect(APP_CONFIG.dataSource.api.baidu.timeout).toBe(10_000);
      expect(APP_CONFIG.dataSource.api.baidu.retryCount).toBe(3);
    });

    it("应该包含回测配置", () => {
      expect(APP_CONFIG.backtest).toBeDefined();
      expect(APP_CONFIG.backtest.maxPeriodYears).toBe(10);
      expect(APP_CONFIG.backtest.minInvestment).toBe(100);
      expect(APP_CONFIG.backtest.maxInvestment).toBe(1_000_000);
      expect(APP_CONFIG.backtest.frequencies).toEqual([
        "weekly",
        "monthly",
        "quarterly",
      ]);
    });

    it("应该包含风险指标配置", () => {
      expect(APP_CONFIG.backtest.riskMetrics).toBeDefined();
      expect(APP_CONFIG.backtest.riskMetrics.riskFreeRate).toBe(0.03);
      expect(APP_CONFIG.backtest.riskMetrics.annualizationFactor).toBe(252);
      expect(APP_CONFIG.backtest.riskMetrics.confidenceLevel).toBe(0.95);
    });

    it("应该包含UI配置", () => {
      expect(APP_CONFIG.ui).toBeDefined();
      expect(APP_CONFIG.ui.theme.primary).toBe("#3b82f6");
      expect(APP_CONFIG.ui.chart.width).toBe(800);
      expect(APP_CONFIG.ui.chart.height).toBe(400);
    });

    it("应该包含功能开关", () => {
      expect(APP_CONFIG.features).toBeDefined();
      expect(APP_CONFIG.features.strategyComparison).toBe(false);
      expect(APP_CONFIG.features.reportExport).toBe(false);
      expect(APP_CONFIG.features.userAccounts).toBe(false);
    });

    it("应该包含验证规则", () => {
      expect(APP_CONFIG.validation).toBeDefined();
      expect(APP_CONFIG.validation.dateFormat).toBe("YYYY-MM-DD");
      expect(APP_CONFIG.validation.amount.min).toBe(100);
      expect(APP_CONFIG.validation.amount.max).toBe(10_000_000);
    });

    it("应该包含错误消息", () => {
      expect(APP_CONFIG.errorMessages).toBeDefined();
      expect(APP_CONFIG.errorMessages.network).toContain("网络连接失败");
      expect(APP_CONFIG.errorMessages.dataLoad).toContain("数据加载失败");
      expect(APP_CONFIG.errorMessages.validation).toContain("参数验证失败");
    });
  });

  describe("ENV_CONFIG", () => {
    it("应该读取环境变量", () => {
      process.env.NEXT_PUBLIC_API_BASE_URL = "https://api.example.com";
      process.env.NEXT_PUBLIC_API_KEY = "test-key";
      process.env.DATABASE_URL = "postgres://localhost:5432/test";

      const { ENV_CONFIG: envConfig } = require("@/lib/config");
      expect(envConfig.apiBaseUrl).toBe("https://api.example.com");
      expect(envConfig.apiKey).toBe("test-key");
      expect(envConfig.databaseUrl).toBe("postgres://localhost:5432/test");
    });
  });

  describe("getConfig", () => {
    it("应该获取顶级配置", () => {
      const name = getConfig("name");
      expect(name).toBe("基金投资策略回测计算器");
    });

    it("应该获取嵌套配置", () => {
      const startDate = getConfig("defaults.startDate");
      expect(startDate).toBe("2020-01-01");

      const primaryColor = getConfig("ui.theme.primary");
      expect(primaryColor).toBe("#3b82f6");

      const riskFreeRate = getConfig("backtest.riskMetrics.riskFreeRate");
      expect(riskFreeRate).toBe(0.03);
    });

    it("应该返回默认值当配置不存在时", () => {
      const nonExistent = getConfig("nonexistent.path", "default");
      expect(nonExistent).toBe("default");

      const deepNonExistent = getConfig("some.very.deep.nonexistent.path", 42);
      expect(deepNonExistent).toBe(42);
    });

    it("应该返回undefined当配置不存在且没有默认值时", () => {
      const nonExistent = getConfig("nonexistent.path");
      expect(nonExistent).toBeUndefined();
    });

    it("应该处理空路径", () => {
      const result = getConfig("", "default");
      expect(result).toBe("default");
    });

    it("应该处理数组配置", () => {
      const frequencies = getConfig("backtest.frequencies");
      expect(frequencies).toEqual(["weekly", "monthly", "quarterly"]);
    });

    it("应该处理对象配置", () => {
      const theme = getConfig("ui.theme");
      expect(theme).toEqual({
        primary: "#3b82f6",
        secondary: "#64748b",
        success: "#10b981",
        warning: "#f59e0b",
        error: "#ef4444",
      });
    });
  });

  describe("validateConfig", () => {
    it("应该验证通过当所有必要配置存在时", () => {
      const isValid = validateConfig();
      expect(isValid).toBe(true);
    });

    it("应该处理验证错误", () => {
      // Mock console.error to avoid noise in tests
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // 临时修改配置使验证失败
      const originalName = APP_CONFIG.name;
      (APP_CONFIG as any).name = undefined;

      const isValid = validateConfig();
      expect(isValid).toBe(false);
      expect(consoleSpy).toHaveBeenCalledWith("Missing required config: name");

      // 恢复配置
      (APP_CONFIG as any).name = originalName;
      consoleSpy.mockRestore();
    });

    it("应该处理异常情况", () => {
      const consoleSpy = vi
        .spyOn(console, "error")
        .mockImplementation(() => {});

      // Mock getConfig to throw an error
      const originalGetConfig = getConfig;
      vi.doMock("@/lib/config", () => ({
        ...require("@/lib/config"),
        getConfig: vi.fn().mockImplementation(() => {
          throw new Error("Test error");
        }),
      }));

      const { validateConfig: mockValidateConfig } = require("@/lib/config");
      const isValid = mockValidateConfig();
      expect(isValid).toBe(false);

      consoleSpy.mockRestore();
    });
  });

  describe("数据源配置", () => {
    it("应该包含缓存配置", () => {
      expect(APP_CONFIG.dataSource.cache.enabled).toBe(true);
      expect(APP_CONFIG.dataSource.cache.ttl).toBe(5 * 60 * 1000);
      expect(APP_CONFIG.dataSource.cache.maxSize).toBe(100);
    });

    it("应该包含数据源优先级", () => {
      expect(APP_CONFIG.dataSource.priority).toEqual([
        "baidu",
        "alternative",
        "mock",
      ]);
    });
  });

  describe("开发配置", () => {
    it("应该包含开发相关配置", () => {
      expect(APP_CONFIG.development.showPerformance).toBe(false);
      expect(APP_CONFIG.development.hotReload).toBe(true);
      expect(APP_CONFIG.development.logLevel).toBe("info");
    });
  });
});
