import type {
  BacktestResult,
  StrategyParams,
  FundData,
  IndexData,
  Fund,
  FixedAmountParams,
  ValueAveragingParams,
  SmartFixedParams,
  GridTradingParams,
  MomentumParams,
  MeanReversionParams,
} from "@/types/fund";

// 回测引擎函数

/**
 * 创建回测引擎配置
 */
function createBacktestEngine(fundData: FundData[], indexData?: IndexData[]) {
  const sortedFundData = fundData.sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );
  const sortedIndexData = indexData?.sort(
    (a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  return {
    fundData: sortedFundData,
    indexData: sortedIndexData,
  };
}

/**
 * 执行回测
 */
async function runBacktest(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  fund: Fund,
  parameters: StrategyParams
): Promise<BacktestResult> {
  const timeline = calculateTimeline(engineConfig, parameters);
  const performance = calculatePerformance(timeline);
  const performanceComparison = generatePerformanceComparison(
    engineConfig,
    timeline
  );

  return {
    strategy: getStrategyType(parameters),
    fund,
    params: parameters,
    performance,
    timeline,
    performanceComparison,
  };
}

/**
 * 计算时间线数据
 */
function calculateTimeline(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  parameters: StrategyParams
) {
  const startDate = new Date(parameters.startDate);
  const endDate = new Date(parameters.endDate);
  const timeline: BacktestResult["timeline"] = [];

  let totalInvestment = 0;
  let totalShares = 0;
  let currentDate = new Date(startDate);

  // 初始投资
  if (parameters.initialAmount > 0) {
    const initialNav = getNavOnDate(engineConfig, currentDate);
    if (initialNav) {
      totalInvestment = parameters.initialAmount;
      totalShares = parameters.initialAmount / initialNav.netAssetValue;

      timeline.push({
        date: currentDate.toISOString().split("T")[0],
        investment: parameters.initialAmount,
        totalInvestment,
        shares: totalShares,
        value: totalShares * initialNav.netAssetValue,
        return: 0,
        netAssetValue: initialNav.netAssetValue,
      });
    }
  }

  // 根据策略类型计算后续投资
  while (currentDate <= endDate) {
    const investment = calculateInvestmentAmount(
      engineConfig,
      parameters,
      currentDate,
      timeline
    );

    if (investment > 0) {
      const nav = getNavOnDate(engineConfig, currentDate);
      if (nav) {
        const newShares = investment / nav.netAssetValue;
        totalShares += newShares;
        totalInvestment += investment;

        const currentValue = totalShares * nav.netAssetValue;
        const returnRate =
          totalInvestment > 0
            ? ((currentValue - totalInvestment) / totalInvestment) * 100
            : 0;

        timeline.push({
          date: currentDate.toISOString().split("T")[0],
          investment,
          totalInvestment,
          shares: totalShares,
          value: currentValue,
          return: returnRate,
          netAssetValue: nav.netAssetValue,
        });
      }
    }

    // 移动到下一个投资日期
    currentDate = getNextInvestmentDate(currentDate, parameters);
  }

  return timeline;
}

/**
 * 根据策略计算投资金额
 */
function calculateInvestmentAmount(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  parameters: StrategyParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  const strategyType = getStrategyType(parameters);

  switch (strategyType) {
    case "fixed_amount": {
      return calculateFixedAmountInvestment(
        parameters as FixedAmountParams,
        date,
        timeline
      );
    }

    case "value_averaging": {
      return calculateValueAveragingInvestment(
        parameters as ValueAveragingParams,
        date,
        timeline
      );
    }

    case "smart_fixed": {
      return calculateSmartFixedInvestment(
        parameters as SmartFixedParams,
        date,
        timeline
      );
    }

    case "grid_trading": {
      return calculateGridTradingInvestment(
        engineConfig,
        parameters as GridTradingParams,
        date,
        timeline
      );
    }

    case "momentum": {
      return calculateMomentumInvestment(
        engineConfig,
        parameters as MomentumParams,
        date,
        timeline
      );
    }

    case "mean_reversion": {
      return calculateMeanReversionInvestment(
        engineConfig,
        parameters as MeanReversionParams,
        date,
        timeline
      );
    }

    default: {
      return 0;
    }
  }
}

/**
 * 定投策略计算
 */
function calculateFixedAmountInvestment(
  parameters: FixedAmountParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  // 跳过初始投资日期
  if (
    timeline.length === 0 ||
    date.getTime() === new Date(parameters.startDate).getTime()
  ) {
    return 0;
  }
  return parameters.monthlyAmount;
}

/**
 * 价值平均策略计算
 */
function calculateValueAveragingInvestment(
  parameters: ValueAveragingParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  if (timeline.length === 0) return 0;

  const lastEntry = timeline.at(-1);
  const monthsElapsed = getMonthsBetween(new Date(parameters.startDate), date);
  const monthlyGrowthRate = parameters.targetGrowthRate / 12 / 100;

  const targetValue =
    parameters.initialAmount * Math.pow(1 + monthlyGrowthRate, monthsElapsed);
  const currentValue = lastEntry.value;

  const requiredInvestment = targetValue - currentValue;
  return Math.min(Math.max(requiredInvestment, 0), parameters.maxInvestment);
}

/**
 * 智能定投策略计算
 */
function calculateSmartFixedInvestment(
  parameters: SmartFixedParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  if (timeline.length === 0) return 0;

  const valuationMultiplier = getValuationMultiplier(
    parameters.valuationMetric,
    date
  );
  return (
    parameters.baseAmount * valuationMultiplier * parameters.adjustmentFactor
  );
}

/**
 * 网格交易策略计算
 */
function calculateGridTradingInvestment(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  parameters: GridTradingParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  const nav = getNavOnDate(engineConfig, date);
  if (!nav) return 0;

  // 获取价格区间
  const priceRange = extractPriceRange(engineConfig, parameters);
  const currentPrice = nav.netAssetValue;

  // 检查价格是否在网格范围内
  if (currentPrice < priceRange.min || currentPrice > priceRange.max) {
    return 0; // 价格超出网格范围，不进行交易
  }

  // 计算网格间距
  const gridSpacing = (priceRange.max - priceRange.min) / parameters.gridCount;

  // 计算当前价格所在的网格位置
  const gridPosition = Math.floor(
    (currentPrice - priceRange.min) / gridSpacing
  );

  // 获取网格状态
  const gridState = getGridState(timeline, parameters);

  // 网格交易逻辑
  return executeGridTrading(
    parameters,
    currentPrice,
    gridPosition,
    gridState,
    timeline
  );
}

/**
 * 提取价格区间参数
 */
function extractPriceRange(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  parameters: GridTradingParams
): { min: number; max: number } {
  // 从参数中提取价格区间，如果没有设置则使用历史数据计算
  if ("priceRange" in parameters && parameters.priceRange) {
    return parameters.priceRange;
  }

  // 从策略参数中提取（新的参数结构）
  const parametersAny = parameters as any;
  if (parametersAny.priceRangeMin && parametersAny.priceRangeMax) {
    return {
      min: parametersAny.priceRangeMin,
      max: parametersAny.priceRangeMax,
    };
  }

  // 如果没有设置价格区间，使用历史数据计算
  return calculatePriceRangeFromHistory(engineConfig);
}

/**
 * 从历史数据计算价格区间
 */
function calculatePriceRangeFromHistory(engineConfig: {
  fundData: FundData[];
  indexData?: IndexData[];
}): { min: number; max: number } {
  const prices = engineConfig.fundData.map((d) => d.netAssetValue);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);

  // 添加10%的缓冲区间
  const buffer = (maxPrice - minPrice) * 0.1;

  return {
    min: Math.max(minPrice - buffer, 0.1),
    max: maxPrice + buffer,
  };
}

/**
 * 获取网格状态
 */
function getGridState(
  timeline: BacktestResult["timeline"],
  parameters: GridTradingParams
) {
  // 简化的网格状态，实际应该跟踪每个网格的持仓情况
  const lastEntry = timeline.at(-1);
  return {
    totalShares: lastEntry?.shares || 0,
    totalInvestment: lastEntry?.totalInvestment || 0,
    availableCash: parameters.initialAmount - (lastEntry?.totalInvestment || 0),
  };
}

/**
 * 执行网格交易逻辑
 */
function executeGridTrading(
  parameters: GridTradingParams,
  currentPrice: number,
  gridPosition: number,
  gridState: any,
  timeline: BacktestResult["timeline"]
): number {
  // 检查是否需要调仓
  if (!shouldRebalance(parameters, timeline)) {
    return 0;
  }

  const priceRange = extractPriceRange(
    { fundData: [], indexData: [] },
    parameters
  );
  const gridSpacing = (priceRange.max - priceRange.min) / parameters.gridCount;

  // 计算目标网格价格
  const targetGridPrice = priceRange.min + (gridPosition + 0.5) * gridSpacing;

  // 网格交易策略：
  // 1. 当价格低于网格中心时，买入
  // 2. 当价格高于网格中心时，卖出（减少投资）

  if (currentPrice < targetGridPrice) {
    // 价格较低，增加投资
    return Math.min(parameters.investmentPerGrid, gridState.availableCash);
  } else if (currentPrice > targetGridPrice * 1.1) {
    // 价格较高，减少投资（通过返回负值表示卖出）
    // 注意：这里简化处理，实际应该实现卖出逻辑
    return 0;
  }

  return 0;
}

/**
 * 检查是否应该调仓
 */
function shouldRebalance(
  parameters: GridTradingParams,
  timeline: BacktestResult["timeline"]
): boolean {
  if (timeline.length === 0) return true;

  const lastEntry = timeline.at(-1);
  const lastDate = new Date(lastEntry!.date);
  const currentDate = new Date();

  switch (parameters.rebalanceFrequency) {
    case "daily": {
      return true;
    } // 每次都检查
    case "weekly": {
      // 检查是否过了一周
      const weeksDiff = Math.floor(
        (currentDate.getTime() - lastDate.getTime()) / (7 * 24 * 60 * 60 * 1000)
      );
      return weeksDiff >= 1;
    }
    case "monthly": {
      // 检查是否过了一个月
      return (
        currentDate.getMonth() !== lastDate.getMonth() ||
        currentDate.getFullYear() !== lastDate.getFullYear()
      );
    }
    default: {
      return false;
    }
  }
}

/**
 * 动量策略计算
 */
function calculateMomentumInvestment(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  parameters: MomentumParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  const momentum = calculateMomentum(
    engineConfig,
    date,
    parameters.lookbackPeriod
  );
  if (momentum > parameters.threshold) {
    return 2000; // 上涨动量，增加投资
  } else if (momentum < -parameters.threshold) {
    return 500; // 下跌动量，减少投资
  }
  return 1000; // 中性动量，正常投资
}

/**
 * 均值回归策略计算
 */
function calculateMeanReversionInvestment(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  parameters: MeanReversionParams,
  date: Date,
  timeline: BacktestResult["timeline"]
): number {
  const deviation = calculateDeviationFromMA(
    engineConfig,
    date,
    parameters.movingAveragePeriod
  );
  if (deviation < -parameters.deviationThreshold) {
    return 2000; // 低于均值，增加投资
  } else if (deviation > parameters.deviationThreshold) {
    return 500; // 高于均值，减少投资
  }
  return 1000; // 接近均值，正常投资
}

/**
 * 辅助方法
 */
function getStrategyType(parameters: StrategyParams): string {
  if ("monthlyAmount" in parameters) return "fixed_amount";
  if ("targetGrowthRate" in parameters) return "value_averaging";
  if ("baseAmount" in parameters) return "smart_fixed";
  if ("gridCount" in parameters) return "grid_trading";
  if ("lookbackPeriod" in parameters) return "momentum";
  if ("movingAveragePeriod" in parameters) return "mean_reversion";
  return "fixed_amount";
}

function getNavOnDate(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  date: Date
): FundData | null {
  const dateString = date.toISOString().split("T")[0];
  return engineConfig.fundData.find((d) => d.date === dateString) || null;
}

function getNextInvestmentDate(
  currentDate: Date,
  parameters: StrategyParams
): Date {
  const frequency =
    "frequency" in parameters ? parameters.frequency : "monthly";
  const nextDate = new Date(currentDate);

  switch (frequency) {
    case "weekly": {
      nextDate.setDate(nextDate.getDate() + 7);
      break;
    }
    case "monthly": {
      nextDate.setMonth(nextDate.getMonth() + 1);
      break;
    }
    case "quarterly": {
      nextDate.setMonth(nextDate.getMonth() + 3);
      break;
    }
  }

  return nextDate;
}

function getMonthsBetween(startDate: Date, endDate: Date): number {
  return (
    (endDate.getFullYear() - startDate.getFullYear()) * 12 +
    (endDate.getMonth() - startDate.getMonth())
  );
}

function getValuationMultiplier(metric: string, date: Date): number {
  // 简化实现，实际应该根据指数数据计算
  return Math.random() * 0.5 + 0.75; // 0.75-1.25之间的随机数
}

function calculateMomentum(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  date: Date,
  lookbackPeriod: number
): number {
  // 简化实现，计算价格动量
  const endDate = new Date(date);
  const startDate = new Date(date);
  startDate.setDate(startDate.getDate() - lookbackPeriod);

  const endNav = getNavOnDate(engineConfig, endDate);
  const startNav = getNavOnDate(engineConfig, startDate);

  if (!endNav || !startNav) return 0;

  return (
    ((endNav.netAssetValue - startNav.netAssetValue) / startNav.netAssetValue) *
    100
  );
}

function calculateDeviationFromMA(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  date: Date,
  period: number
): number {
  // 简化实现，计算与移动平均的偏离
  const nav = getNavOnDate(engineConfig, date);
  if (!nav) return 0;

  // 这里应该计算真实的移动平均，暂时简化
  const ma = nav.netAssetValue * (0.95 + Math.random() * 0.1); // 模拟移动平均
  return ((nav.netAssetValue - ma) / ma) * 100;
}

function calculatePerformance(timeline: BacktestResult["timeline"]) {
  if (timeline.length === 0) {
    return {
      totalReturn: 0,
      annualizedReturn: 0,
      volatility: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      totalInvestment: 0,
      finalValue: 0,
    };
  }

  const firstEntry = timeline[0];
  const lastEntry = timeline.at(-1);

  const totalReturn =
    ((lastEntry.value - lastEntry.totalInvestment) /
      lastEntry.totalInvestment) *
    100;
  const years =
    (new Date(lastEntry.date).getTime() - new Date(firstEntry.date).getTime()) /
    (365.25 * 24 * 60 * 60 * 1000);
  const annualizedReturn =
    Math.pow(lastEntry.value / lastEntry.totalInvestment, 1 / years) - 1;

  // 计算波动率
  const returns = timeline
    .slice(1)
    .map(
      (entry, index) =>
        (entry.value - timeline[index].value) / timeline[index].value
    );
  const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance =
    returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) /
    returns.length;
  const volatility = Math.sqrt(variance) * Math.sqrt(252) * 100; // 年化波动率

  // 计算最大回撤
  let maxDrawdown = 0;
  let peak = timeline[0].value;
  for (const entry of timeline) {
    if (entry.value > peak) {
      peak = entry.value;
    }
    const drawdown = (peak - entry.value) / peak;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }

  const sharpeRatio =
    volatility > 0 ? (annualizedReturn * 100 - 3) / volatility : 0; // 假设无风险利率3%

  return {
    totalReturn,
    annualizedReturn: annualizedReturn * 100,
    volatility,
    sharpeRatio,
    maxDrawdown: maxDrawdown * 100,
    totalInvestment: lastEntry.totalInvestment,
    finalValue: lastEntry.value,
  };
}

/**
 * 生成收益率对比数据
 */
function generatePerformanceComparison(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  timeline: BacktestResult["timeline"]
) {
  if (timeline.length === 0) {
    return [];
  }

  // 获取起始净值和指数值
  const startNav = timeline[0].netAssetValue;
  const startIndexValue = getIndexValueOnDate(
    engineConfig,
    new Date(timeline[0].date)
  );

  return timeline.map((entry) => {
    // 策略收益率 = (当前价值 - 累计投入) / 累计投入 * 100
    const strategyReturn =
      entry.totalInvestment > 0
        ? ((entry.value - entry.totalInvestment) / entry.totalInvestment) * 100
        : 0;

    // 基金收益率 = (当前净值 - 起始净值) / 起始净值 * 100
    const fundReturn = ((entry.netAssetValue - startNav) / startNav) * 100;

    // 指数收益率
    let indexReturn = 0;
    if (startIndexValue && startIndexValue > 0) {
      const currentIndexValue = getIndexValueOnDate(
        engineConfig,
        new Date(entry.date)
      );
      if (currentIndexValue) {
        indexReturn =
          ((currentIndexValue - startIndexValue) / startIndexValue) * 100;
      }
    }

    return {
      date: entry.date,
      strategyReturn,
      fundReturn,
      indexReturn,
    };
  });
}

/**
 * 获取指定日期的指数值
 */
function getIndexValueOnDate(
  engineConfig: { fundData: FundData[]; indexData?: IndexData[] },
  date: Date
): number | null {
  if (!engineConfig.indexData || engineConfig.indexData.length === 0) {
    return null;
  }

  const dateString = date.toISOString().split("T")[0];
  const indexEntry = engineConfig.indexData.find((d) => d.date === dateString);

  if (indexEntry) {
    return indexEntry.value;
  }

  // 如果找不到精确日期，找最近的日期
  const sortedIndexData = [...engineConfig.indexData].sort(
    (a, b) =>
      Math.abs(new Date(a.date).getTime() - date.getTime()) -
      Math.abs(new Date(b.date).getTime() - date.getTime())
  );

  return sortedIndexData[0]?.value || null;
}

// 保持向后兼容性的类包装器
export class BacktestEngine {
  private engineConfig: { fundData: FundData[]; indexData?: IndexData[] };

  constructor(fundData: FundData[], indexData?: IndexData[]) {
    this.engineConfig = createBacktestEngine(fundData, indexData);
  }

  async runBacktest(
    fund: Fund,
    parameters: StrategyParams
  ): Promise<BacktestResult> {
    return runBacktest(this.engineConfig, fund, parameters);
  }
}
