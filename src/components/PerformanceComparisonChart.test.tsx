import { render, screen } from "@testing-library/react";
import { describe, it, expect, vi, beforeEach } from "vitest";

import PerformanceComparisonChart from "@/components/PerformanceComparisonChart";
import type { BacktestResult } from "@/types/fund";

// Mock recharts components
vi.mock("recharts", () => ({
  LineChart: ({ children }: any) => (
    <div data-testid="line-chart">{children}</div>
  ),
  Line: ({ name }: any) => <div data-testid="line">{name}</div>,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => (
    <div data-testid="responsive-container">{children}</div>
  ),
  ReferenceLine: () => <div data-testid="reference-line" />,
}));

const mockBacktestResult: BacktestResult = {
  strategy: "fixed_amount",
  fund: {
    id: "000001",
    name: "测试基金",
    code: "000001",
    type: "股票型",
    riskLevel: "高风险",
  },
  params: {
    startDate: "2024-01-01",
    endDate: "2024-03-01",
    initialAmount: 1000,
    monthlyAmount: 500,
    frequency: "monthly",
  },
  performance: {
    totalInvestment: 2000,
    finalValue: 2100,
    totalReturn: 5,
    annualizedReturn: 20,
    maxDrawdown: -3.5,
    volatility: 15.2,
    sharpeRatio: 1.31,
  },
  timeline: [
    {
      date: "2024-01-01",
      investment: 1000,
      shares: 1000,
      value: 1000,
      totalInvestment: 1000,
      netAssetValue: 1,
    },
    {
      date: "2024-02-01",
      investment: 500,
      shares: 495.05,
      value: 1515.15,
      totalInvestment: 1500,
      netAssetValue: 1.01,
    },
    {
      date: "2024-03-01",
      investment: 500,
      shares: 476.19,
      value: 2100,
      totalInvestment: 2000,
      netAssetValue: 1.05,
    },
  ],
};

const mockBacktestResultWithComparison: BacktestResult = {
  ...mockBacktestResult,
  performanceComparison: [
    {
      date: "2024-01-01",
      strategyReturn: 0,
      fundReturn: 0,
      indexReturn: 0,
    },
    {
      date: "2024-02-01",
      strategyReturn: 1.01,
      fundReturn: 1,
      indexReturn: 0.8,
    },
    {
      date: "2024-03-01",
      strategyReturn: 5,
      fundReturn: 5,
      indexReturn: 4.2,
    },
  ],
};

describe("PerformanceComparisonChart", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该渲染图表组件", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
    expect(screen.getByTestId("responsive-container")).toBeInTheDocument();
    expect(screen.getByTestId("line-chart")).toBeInTheDocument();
  });

  it("应该显示性能指标卡片", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByText("策略收益率")).toBeInTheDocument();
    expect(screen.getByText("5.00%")).toBeInTheDocument();
    expect(screen.getByText("基金收益率")).toBeInTheDocument();
    expect(screen.getByText("指数收益率")).toBeInTheDocument();
  });

  it("应该渲染图表元素", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByTestId("x-axis")).toBeInTheDocument();
    expect(screen.getByTestId("y-axis")).toBeInTheDocument();
    expect(screen.getByTestId("cartesian-grid")).toBeInTheDocument();
    expect(screen.getByTestId("tooltip")).toBeInTheDocument();
    expect(screen.getByTestId("legend")).toBeInTheDocument();
    expect(screen.getByTestId("reference-line")).toBeInTheDocument();
  });

  it("应该显示策略收益率线", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByText("策略收益率")).toBeInTheDocument();
  });

  it("应该显示基金收益率线", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByText("基金收益率")).toBeInTheDocument();
  });

  it("应该显示指数收益率线", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByText("指数收益率")).toBeInTheDocument();
  });

  it("应该使用提供的对比数据", () => {
    render(
      <PerformanceComparisonChart result={mockBacktestResultWithComparison} />
    );

    // 验证组件正常渲染
    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
    expect(screen.getByTestId("line-chart")).toBeInTheDocument();
  });

  it("应该生成对比数据当没有提供时", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    // 验证组件正常渲染，说明成功生成了对比数据
    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
    expect(screen.getByTestId("line-chart")).toBeInTheDocument();
  });

  it("应该处理空时间线数据", () => {
    const emptyResult: BacktestResult = {
      ...mockBacktestResult,
      timeline: [],
    };

    render(<PerformanceComparisonChart result={emptyResult} />);

    // 组件应该仍然渲染，但可能显示空状态
    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
  });

  it("应该正确格式化百分比", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    // 验证百分比格式化
    expect(screen.getByText("5.00%")).toBeInTheDocument();
  });

  it("应该显示正确的性能指标", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    // 验证各种性能指标
    expect(screen.getByText("策略收益率")).toBeInTheDocument();
    expect(screen.getByText("基金收益率")).toBeInTheDocument();
    expect(screen.getByText("指数收益率")).toBeInTheDocument();
  });

  it("应该处理负收益率", () => {
    const negativeResult: BacktestResult = {
      ...mockBacktestResult,
      performance: {
        ...mockBacktestResult.performance,
        totalReturn: -5,
      },
      timeline: [
        {
          date: "2024-01-01",
          investment: 1000,
          shares: 1000,
          value: 950,
          totalInvestment: 1000,
          netAssetValue: 0.95,
        },
      ],
    };

    render(<PerformanceComparisonChart result={negativeResult} />);

    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
  });

  it("应该处理极值数据", () => {
    const extremeResult: BacktestResult = {
      ...mockBacktestResult,
      performance: {
        ...mockBacktestResult.performance,
        totalReturn: 999.99,
      },
    };

    render(<PerformanceComparisonChart result={extremeResult} />);

    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
  });

  it("应该显示图表说明", () => {
    render(<PerformanceComparisonChart result={mockBacktestResult} />);

    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
  });

  it("应该处理单个数据点", () => {
    const singlePointResult: BacktestResult = {
      ...mockBacktestResult,
      timeline: [
        {
          date: "2024-01-01",
          investment: 1000,
          shares: 1000,
          value: 1000,
          totalInvestment: 1000,
          netAssetValue: 1,
        },
      ],
    };

    render(<PerformanceComparisonChart result={singlePointResult} />);

    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
    expect(screen.getByTestId("line-chart")).toBeInTheDocument();
  });

  it("应该处理数据中的零值", () => {
    const zeroValueResult: BacktestResult = {
      ...mockBacktestResult,
      timeline: [
        {
          date: "2024-01-01",
          investment: 0,
          shares: 0,
          value: 0,
          totalInvestment: 0,
          netAssetValue: 1,
        },
      ],
    };

    render(<PerformanceComparisonChart result={zeroValueResult} />);

    expect(screen.getByText("收益率对比分析")).toBeInTheDocument();
  });
});
