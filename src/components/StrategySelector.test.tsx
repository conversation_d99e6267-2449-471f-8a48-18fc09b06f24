import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi } from "vitest";

import type { Strategy } from "@/types/fund";

import StrategySelector from "./StrategySelector";

describe("StrategySelector", () => {
  const mockOnStrategySelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该渲染所有策略选项", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("定投策略")).toBeInTheDocument();
    expect(screen.getByText("价值平均策略")).toBeInTheDocument();
    expect(screen.getByText("智能定投")).toBeInTheDocument();
    expect(screen.getByText("网格交易")).toBeInTheDocument();
    expect(screen.getByText("动量策略")).toBeInTheDocument();
    expect(screen.getByText("均值回归")).toBeInTheDocument();
  });

  it("应该显示当前选中的策略", () => {
    render(
      <StrategySelector
        selectedStrategy="value_averaging"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    const valueAveragingOption = screen.getByRole("radio", {
      name: /价值平均策略/,
    });
    expect(valueAveragingOption).toBeChecked();
  });

  it("应该在策略选择时调用回调函数", async () => {
    const user = userEvent.setup();

    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    const gridTradingOption = screen.getByRole("radio", { name: /网格交易/ });
    await user.click(gridTradingOption);

    expect(mockOnStrategySelect).toHaveBeenCalledWith("grid_trading");
  });

  it("应该显示策略描述", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText(/固定金额定期投资/)).toBeInTheDocument();
    expect(screen.getByText(/根据目标增长率调整投资金额/)).toBeInTheDocument();
    expect(screen.getByText(/基于估值指标调整投资金额/)).toBeInTheDocument();
  });

  it("应该显示策略风险等级", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("低风险")).toBeInTheDocument();
    expect(screen.getByText("中风险")).toBeInTheDocument();
    expect(screen.getByText("高风险")).toBeInTheDocument();
  });

  it("应该显示策略适用性", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText(/适合长期稳健投资/)).toBeInTheDocument();
    expect(screen.getByText(/适合有经验的投资者/)).toBeInTheDocument();
    expect(screen.getByText(/适合震荡市场/)).toBeInTheDocument();
  });

  it("应该支持键盘导航", async () => {
    const user = userEvent.setup();

    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    const firstOption = screen.getByRole("radio", { name: /定投策略/ });
    firstOption.focus();

    await user.keyboard("{ArrowDown}");

    const secondOption = screen.getByRole("radio", { name: /价值平均策略/ });
    expect(secondOption).toHaveFocus();
  });

  it("应该显示策略图标", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    // 检查是否有图标元素（通过类名或测试ID）
    expect(
      screen.getByTestId("strategy-icon-fixed_amount")
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("strategy-icon-value_averaging")
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("strategy-icon-grid_trading")
    ).toBeInTheDocument();
  });

  it("应该处理禁用状态", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
        disabled={true}
      />
    );

    const options = screen.getAllByRole("radio");
    for (const option of options) {
      expect(option).toBeDisabled();
    }
  });

  it("应该显示策略参数提示", () => {
    render(
      <StrategySelector
        selectedStrategy="grid_trading"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText(/需要设定网格层数和间距/)).toBeInTheDocument();
  });

  it("应该支持策略过滤", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
        filter="low_risk"
      />
    );

    // 只显示低风险策略
    expect(screen.getByText("定投策略")).toBeInTheDocument();
    expect(screen.queryByText("网格交易")).not.toBeInTheDocument();
  });

  it("应该显示策略性能指标", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText(/历史年化收益/)).toBeInTheDocument();
    expect(screen.getByText(/最大回撤/)).toBeInTheDocument();
    expect(screen.getByText(/胜率/)).toBeInTheDocument();
  });

  it("应该处理策略变更确认", async () => {
    const user = userEvent.setup();

    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
        requireConfirmation={true}
      />
    );

    const riskStrategy = screen.getByRole("radio", { name: /网格交易/ });
    await user.click(riskStrategy);

    // 应该显示确认对话框
    expect(screen.getByText(/确认切换到高风险策略/)).toBeInTheDocument();

    const confirmButton = screen.getByRole("button", { name: /确认/ });
    await user.click(confirmButton);

    expect(mockOnStrategySelect).toHaveBeenCalledWith("grid_trading");
  });

  it("应该显示策略比较功能", () => {
    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
        showComparison={true}
      />
    );

    expect(screen.getByText("策略对比")).toBeInTheDocument();
    expect(
      screen.getByRole("button", { name: /添加对比/ })
    ).toBeInTheDocument();
  });

  it("应该处理自定义策略", () => {
    const customStrategies: Strategy[] = [
      {
        id: "custom_strategy",
        name: "自定义策略",
        description: "用户自定义的投资策略",
        riskLevel: "medium",
        category: "custom",
      },
    ];

    render(
      <StrategySelector
        selectedStrategy="fixed_amount"
        onStrategySelect={mockOnStrategySelect}
        customStrategies={customStrategies}
      />
    );

    expect(screen.getByText("自定义策略")).toBeInTheDocument();
  });
});
