// 工具函数库

import { APP_CONFIG } from "./config";

// 数字格式化
export const formatNumber = (
  number_: number,
  decimals = 2,
  locale = "zh-CN"
): string => {
  if (isNaN(number_)) return "--";

  return number_.toLocaleString(locale, {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

// 货币格式化
export const formatCurrency = (
  amount: number,
  currency = "CNY",
  locale = "zh-CN"
): string => {
  if (isNaN(amount)) return "--";

  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// 百分比格式化
export const formatPercent = (value: number, decimals = 2): string => {
  if (isNaN(value)) return "--";

  return `${formatNumber(value * 100, decimals)}%`;
};

import { format, isValid, parseISO } from "date-fns";
import { zhCN } from "date-fns/locale";

// 日期格式化 - 使用 date-fns
export const formatDate = (
  date: string | Date,
  formatString = "yyyy-MM-dd"
): string => {
  try {
    const dateObject = typeof date === "string" ? parseISO(date) : date;
    if (!isValid(dateObject)) return "--";

    // 支持中文格式
    if (formatString === "YYYY年MM月DD日") {
      return format(dateObject, "yyyy年MM月dd日", { locale: zhCN });
    }

    // 转换常用格式
    const formatMap: Record<string, string> = {
      "YYYY-MM-DD": "yyyy-MM-dd",
      "YYYY/MM/DD": "yyyy/MM/dd",
      "MM/DD/YYYY": "MM/dd/yyyy",
      "DD/MM/YYYY": "dd/MM/yyyy",
    };

    const actualFormat = formatMap[formatString] || formatString;
    return format(dateObject, actualFormat, { locale: zhCN });
  } catch (error) {
    console.error("Date formatting error:", error);
    return "--";
  }
};

// 日期范围验证
export const validateDateRange = (
  startDate: string,
  endDate: string
): { isValid: boolean; error?: string } => {
  const start = new Date(startDate);
  const end = new Date(endDate);

  if (isNaN(start.getTime())) {
    throw new TypeError("开始日期格式不正确");
  }

  if (isNaN(end.getTime())) {
    throw new TypeError("结束日期格式不正确");
  }

  if (start >= end) {
    throw new Error("开始日期必须早于结束日期");
  }

  const daysDiff = Math.ceil(
    (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
  );
  const maxDays = APP_CONFIG.validation.period.max;

  if (daysDiff > maxDays) {
    throw new Error(`投资期间不能超过${Math.floor(maxDays / 365)}年`);
  }

  return { isValid: true };
};

// 金额验证
export const validateAmount = (
  amount: number,
  fieldName = "金额"
): { isValid: boolean; error?: string } => {
  if (isNaN(amount)) {
    throw new TypeError(`${fieldName}必须是数字`);
  }

  if (amount < 0) {
    throw new Error("金额不能为负数");
  }

  const { min, max } = APP_CONFIG.validation.amount;

  if (amount < min) {
    throw new Error("金额不能小于1元");
  }

  if (amount > max) {
    throw new Error(`${fieldName}不能超过${formatCurrency(max)}`);
  }

  return { isValid: true };
};

// 百分比验证
export const validatePercentage = (
  value: number,
  fieldName = "百分比"
): { isValid: boolean; error?: string } => {
  if (isNaN(value)) {
    throw new TypeError(`${fieldName}必须是数字`);
  }

  const { min, max } = APP_CONFIG.validation.percentage;

  if (value < min || value > max) {
    throw new Error(`百分比必须在0-100之间`);
  }

  return { isValid: true };
};

// 计算两个日期之间的月数
export const getMonthsBetween = (startDate: Date, endDate: Date): number => {
  const yearDiff = endDate.getFullYear() - startDate.getFullYear();
  const monthDiff = endDate.getMonth() - startDate.getMonth();
  return yearDiff * 12 + monthDiff;
};

// 计算两个日期之间的天数
export const getDaysBetween = (startDate: Date, endDate: Date): number => {
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
};

// 计算年化收益率
export const calculateAnnualizedReturn = (
  initialValue: number,
  finalValue: number,
  days: number
): number => {
  if (initialValue <= 0 || finalValue <= 0 || days <= 0) return 0;

  const years = days / 365;
  return ((finalValue / initialValue) ** (1 / years) - 1) * 100;
};

// 计算波动率
export const calculateVolatility = (returns: number[]): number => {
  if (returns.length < 2) return 0;

  const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
  const variance =
    returns.reduce((sum, r) => sum + (r - mean) ** 2, 0) / returns.length;

  return Math.sqrt(variance) * Math.sqrt(252) * 100; // 年化波动率
};

// 计算最大回撤
export const calculateMaxDrawdown = (values: number[]): number => {
  if (values.length < 2) return 0;

  let maxDrawdown = 0;
  let peak = values[0];

  for (const value of values) {
    if (value > peak) {
      peak = value;
    }

    const drawdown = (peak - value) / peak;
    if (drawdown > maxDrawdown) {
      maxDrawdown = drawdown;
    }
  }

  return maxDrawdown * 100;
};

// 计算夏普比率
export const calculateSharpeRatio = (
  annualizedReturn: number,
  volatility: number,
  riskFreeRate: number = APP_CONFIG.backtest.riskMetrics.riskFreeRate
): number => {
  if (volatility === 0) return 0;

  return (annualizedReturn / 100 - riskFreeRate) / (volatility / 100);
};

// 防抖函数
export const debounce = <T extends (...arguments_: any[]) => any>(
  function_: T,
  delay: number
): ((...arguments_: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;

  return (...arguments_: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => function_(...arguments_), delay);
  };
};

// 节流函数
export const throttle = <T extends (...arguments_: any[]) => any>(
  function_: T,
  delay: number
): ((...arguments_: Parameters<T>) => void) => {
  let lastCall = 0;

  return (...arguments_: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      function_(...arguments_);
    }
  };
};

// 深拷贝
export const deepClone = <T>(object: T): T => {
  if (object === null || typeof object !== "object") return object;

  if (object instanceof Date) return new Date(object) as T;
  if (Array.isArray(object)) return object.map((item) => deepClone(item)) as T;

  const cloned = {} as T;
  for (const key in object) {
    if (object.hasOwnProperty(key)) {
      cloned[key] = deepClone(object[key]);
    }
  }

  return cloned;
};

// 生成唯一ID
export const generateId = (prefix = "id"): string => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).slice(2, 11)}`;
};

// 下载文件
const downloadFile = (
  content: string,
  filename: string,
  type = "text/plain"
): void => {
  const blob = new Blob([content], { type });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.download = filename;
  document.body.append(link);
  link.click();

  link.remove();
  URL.revokeObjectURL(url);
};

// 复制到剪贴板
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // 降级方案
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.append(textArea);
      textArea.select();
      document.execCommand("copy");
      textArea.remove();
      return true;
    }
  } catch (error) {
    console.error("复制失败:", error);
    return false;
  }
};

// 错误处理
export const handleError = (error: unknown, context = ""): string => {
  console.error(`Error in ${context}:`, error);

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === "string") {
    return error;
  }

  return "未知错误";
};
