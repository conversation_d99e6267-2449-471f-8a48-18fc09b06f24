"use client";

import { useState, useEffect } from "react";

import { useF<PERSON>, Controller } from "react-hook-form";

import type { Strategy } from "@/types/fund";

interface ParameterInputsProperties {
  strategy: Strategy;
  parameters: Record<string, any>;
  onParametersChange: (parameters: Record<string, any>) => void;
}

export default function ParameterInputs({
  strategy,
  parameters,
  onParametersChange,
}: ParameterInputsProperties) {
  const [localParameters, setLocalParameters] = useState<Record<string, any>>(
    {}
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 初始化参数
  useEffect(() => {
    const initialParameters: Record<string, any> = {};
    for (const [key, parameter] of Object.entries(strategy.parameterSchema)) {
      initialParameters[key] = parameters[key] ?? parameter.defaultValue ?? "";
    }
    setLocalParameters(initialParameters);
    onParametersChange(initialParameters);
  }, [strategy.id]);

  // 验证参数
  const validateParameter = (key: string, value: any): string => {
    const parameter = strategy.parameterSchema[key];

    if (parameter.required && (!value || value === "")) {
      return `${parameter.label}是必填项`;
    }

    if (parameter.type === "number" && value !== "") {
      const numberValue = Number(value);
      if (isNaN(numberValue)) {
        return `${parameter.label}必须是数字`;
      }
      if (parameter.min !== undefined && numberValue < parameter.min) {
        return `${parameter.label}不能小于${parameter.min}`;
      }
      if (parameter.max !== undefined && numberValue > parameter.max) {
        return `${parameter.label}不能大于${parameter.max}`;
      }
    }

    if (parameter.type === "date" && value) {
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        return `${parameter.label}格式不正确`;
      }
    }

    return "";
  };

  // 处理参数变化
  const handleParameterChange = (key: string, value: any) => {
    const newParameters = { ...localParameters, [key]: value };
    setLocalParameters(newParameters);

    // 验证当前参数
    const error = validateParameter(key, value);
    setErrors((previous) => ({ ...previous, [key]: error }));

    // 如果没有错误，更新父组件
    if (!error) {
      onParametersChange(newParameters);
    }
  };

  // 渲染不同类型的输入组件
  const renderInput = (
    key: string,
    parameter: Strategy["parameterSchema"][string]
  ) => {
    const value = localParameters[key] ?? "";
    const error = errors[key];

    const baseInputClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
      error ? "border-red-300" : "border-gray-300"
    }`;

    switch (parameter.type) {
      case "number": {
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => {
              handleParameterChange(key, e.target.value);
            }}
            min={parameter.min}
            max={parameter.max}
            step={parameter.step}
            className={baseInputClasses}
            placeholder={`请输入${parameter.label}`}
          />
        );
      }

      case "date": {
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => {
              handleParameterChange(key, e.target.value);
            }}
            className={baseInputClasses}
          />
        );
      }

      case "select": {
        return (
          <select
            value={value}
            onChange={(e) => {
              handleParameterChange(key, e.target.value);
            }}
            className={baseInputClasses}
          >
            <option value="">请选择{parameter.label}</option>
            {parameter.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      }

      case "range": {
        return (
          <div className="space-y-2">
            <input
              type="range"
              value={value}
              onChange={(e) => {
                handleParameterChange(key, e.target.value);
              }}
              min={parameter.min}
              max={parameter.max}
              step={parameter.step}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>{parameter.min}</span>
              <span className="font-medium">{value}</span>
              <span>{parameter.max}</span>
            </div>
          </div>
        );
      }

      default: {
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => {
              handleParameterChange(key, e.target.value);
            }}
            className={baseInputClasses}
            placeholder={`请输入${parameter.label}`}
          />
        );
      }
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">策略参数设置</h3>

      <div className="space-y-4">
        {Object.entries(strategy.parameterSchema).map(([key, parameter]) => (
          <div key={key} className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              {parameter.label}
              {parameter.required ? (
                <span className="text-red-500 ml-1">*</span>
              ) : null}
            </label>

            {renderInput(key, parameter)}

            {parameter.description ? (
              <p className="text-sm text-gray-500">{parameter.description}</p>
            ) : null}

            {errors[key] ? (
              <p className="text-sm text-red-600">{errors[key]}</p>
            ) : null}
          </div>
        ))}
      </div>

      {/* 参数预览 */}
      <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">参数预览</h4>
        <div className="space-y-2">
          {Object.entries(strategy.parameterSchema).map(([key, parameter]) => {
            const value = localParameters[key];
            let displayValue = value;

            // 格式化显示值
            if (parameter.type === "select" && parameter.options) {
              const option = parameter.options.find(
                (opt) => opt.value === value
              );
              displayValue = option ? option.label : value;
            } else if (parameter.type === "number" && value) {
              displayValue = Number(value).toLocaleString();
            }

            return (
              <div key={key} className="flex justify-between text-sm">
                <span className="text-gray-600">{parameter.label}:</span>
                <span className="font-medium text-gray-900">
                  {displayValue || "未设置"}
                  {parameter.type === "number" && value ? (
                    <span className="text-gray-500 ml-1">
                      {key.includes("Amount") || key.includes("Investment")
                        ? "元"
                        : key.includes("Rate") || key.includes("Threshold")
                          ? "%"
                          : key.includes("Period")
                            ? "天"
                            : ""}
                    </span>
                  ) : null}
                </span>
              </div>
            );
          })}
        </div>
      </div>

      {/* 验证状态 */}
      {Object.keys(errors).some((key) => errors[key]) && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-red-400 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-sm text-red-800">
              请修正参数错误后再进行回测
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
