// 基金数据API路由

import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

import { validateFund, getFundInfo } from "@/lib/api/fundApi";
import type { BaiduApiResponse } from "@/lib/api/types";
import {
  buildApiUrl,
  parseFundData,
  withRetry,
  withTimeout,
} from "@/lib/api/utils";
import type { FundData } from "@/types/fund";

// 服务端直接调用外部API，避免CORS问题
async function fetchFundDataFromBaidu(
  fundCode: string,
  startDate?: string,
  endDate?: string
): Promise<FundData[]> {
  const url = buildApiUrl(fundCode);

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            Accept: "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.ResultCode !== "0") {
          throw new Error(`API错误: ${data.ResultCode}`);
        }

        return data as BaiduApiResponse;
      },
      { maxRetries: 3, delay: 1000 }
    );

  const rawData = await withTimeout(fetchWithRetry(), 15_000);
  const parsedData = parseFundData(rawData);

  // 转换为项目内部格式
  return parsedData.map((item) => ({
    date: item.date,
    netAssetValue: item.netAssetValue,
    accumulatedValue: item.accumulatedValue,
    dailyGrowthRate: Number.parseFloat(item.dailyChange) || 0,
  }));
}

// GET /api/fund?code=000628&startDate=2024-01-01&endDate=2024-12-31
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fundCode = searchParams.get("code");
    const startDate = searchParams.get("startDate");
    const endDate = searchParams.get("endDate");

    if (!fundCode) {
      return NextResponse.json({ error: "基金代码不能为空" }, { status: 400 });
    }

    // 验证基金代码格式
    if (!/^\d{6}$/.test(fundCode)) {
      return NextResponse.json(
        { error: "基金代码格式错误，应为6位数字" },
        { status: 400 }
      );
    }

    // 服务端获取基金数据
    const fundData: FundData[] = await fetchFundDataFromBaidu(
      fundCode,
      startDate || undefined,
      endDate || undefined
    );

    return NextResponse.json({
      success: true,
      data: fundData,
      meta: {
        fundCode,
        startDate,
        endDate,
        count: fundData.length,
      },
    });
  } catch (error) {
    console.error("获取基金数据失败:", error);

    const errorMessage = error instanceof Error ? error.message : "未知错误";

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        code: "FUND_DATA_ERROR",
      },
      { status: 500 }
    );
  }
}

// POST /api/fund/validate
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { fundCode } = body;

    if (!fundCode) {
      return NextResponse.json({ error: "基金代码不能为空" }, { status: 400 });
    }

    const isValid = await validateFund(fundCode);
    const fundInfo = isValid ? await getFundInfo(fundCode) : null;

    return NextResponse.json({
      success: true,
      data: {
        isValid,
        fundInfo,
      },
    });
  } catch (error) {
    console.error("验证基金代码失败:", error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "验证失败",
        code: "VALIDATION_ERROR",
      },
      { status: 500 }
    );
  }
}
